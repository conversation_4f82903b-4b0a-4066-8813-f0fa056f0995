import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import SellerSidebar from "./SellerSidebar";
import "../../styles/SellerLayout.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdVideoLibrary } from "react-icons/md";

const SellerLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);

  // Map routes to tabs
  const routeToTabMap = {
    "/seller/dashboard": "dashboard",
    "/seller/my-sports-strategies": "my-sports-strategies",
    "/seller/requests": "requests",
    "/seller/bids": "bids",
    "/seller/profile": "profile",
  };

  // Header configuration for each page
  const headerConfig = {
    "dashboard": {
      title: "Dashboard",
    
      icon: <MdDashboard />
    },
    "my-sports-strategies": {
      title: "My Sports Strategies",
     
      icon: <MdVideoLibrary />
    },
    "requests": {
      title: "Requests",
    
      icon: <MdRequestPage />
    },
    "bids": {
      title: "Bids",
    
      icon: <FaGavel />
    },
    "profile": {
      title: "My Profile",
      
      icon: <FaUser />
    }
  };

  // Get current header info
  const currentHeader = headerConfig[activeTab] || headerConfig["dashboard"];

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  return (
    <div className="SellerLayout">
      <div className="container max-container">
        <div className="sidebar">
          <SellerSidebar />
        </div>
        <div className="content">
          <div className="SellerLayout__header">
            <h1 className="SellerLayout__title">{currentHeader.title}</h1>
            
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default SellerLayout;
